['AUTO_MODE', 'ApplyFlatInstructionsRequest', 'ApplyFlatInstructionsResponse', 'CHAMPION', 'CompilationConfigUsed', 'CompilationMode', 'CompileTargetRequest', 'CompileTargetResponse', 'DEBUG_MODE', 'DESCRIPTOR', 'EnhancedStartFuzzerRequest', 'EnhancedStartFuzzerResponse', 'FlatConfigInstruction', 'FlatStrategyConfig', 'FuzzerType', 'GetChampionStateRequest', 'GetChampionStateResponse', 'HealthCheckRequest', 'HealthCheckResponse', 'InstanceStatus', 'InstructionResult', 'PerformanceMetrics', 'PromoteShadowRequest', 'PromoteShadowResponse', 'RELEASE_MODE', 'SHADOW', 'SpawnShadowRequest', 'SpawnShadowResponse', 'StartFuzzerRequest', 'StartFuzzerResponse', 'StopFuzzerRequest', 'StopFuzzerResponse', 'StrategyConfig', 'SyncIncrementalStateRequest', 'SyncIncrementalStateResponse', 'SystemMetrics', 'UpdateStrategyRequest', 'UpdateStrategyResponse', 'ValidateCodeRequest', 'ValidateCodeResponse', 'ValidationResult']
