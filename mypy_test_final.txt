fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "HealthCheckResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "InstanceStatus"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "PerformanceMetrics"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "PromoteShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "PromoteShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "SpawnShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "SpawnShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StopFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StopFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "SystemMetrics"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "UpdateStrategyRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "UpdateStrategyResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "ValidateCodeRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "ValidateCodeResponse"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:61: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:124: error: Module has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:138: error: Module has no attribute "StartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:141: error: Module has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:143: error: Module has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:186: error: Module has no attribute "StopFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:222: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:269: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:319: error: Module has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:332: error: Module has no attribute "UpdateStrategyRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:375: error: Module has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:388: error: Module has no attribute "SpawnShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:428: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:433: error: Module has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:441: error: Module has no attribute "PromoteShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:482: error: Module has no attribute "ValidateCodeRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:531: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:19: error: Module "fuzzlm_agent.infrastructure.shared_memory.telemetry_pb2" has no attribute "TelemetryDataType"  [attr-defined]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:19: error: Module "fuzzlm_agent.infrastructure.shared_memory.telemetry_pb2" has no attribute "TelemetryEntry"  [attr-defined]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:433: error: Returning Any from function declared to return "str"  [no-any-return]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:438: error: Returning Any from function declared to return "int"  [no-any-return]
Found 40 errors in 3 files (checked 28 source files)
