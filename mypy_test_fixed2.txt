fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:6: error: Cannot find implementation or library stub for module named "llm_generation_pb2"  [import-not-found]
fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:6: note: See https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports
fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:32: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:54: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:61: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:69: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:94: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/llm_generation_pb2_grpc.py:121: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:32: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:40: error: Module has no attribute "StartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:41: error: Module has no attribute "StartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:45: error: Module has no attribute "StopFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:46: error: Module has no attribute "StopFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:50: error: Module has no attribute "UpdateStrategyRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:51: error: Module has no attribute "UpdateStrategyResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:55: error: Module has no attribute "SpawnShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:56: error: Module has no attribute "SpawnShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:60: error: Module has no attribute "PromoteShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:61: error: Module has no attribute "PromoteShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:65: error: Module has no attribute "ValidateCodeRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:66: error: Module has no attribute "ValidateCodeResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:70: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:71: error: Module has no attribute "HealthCheckResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:75: error: Module has no attribute "GetChampionStateRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:76: error: Module has no attribute "GetChampionStateResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:80: error: Module has no attribute "SyncIncrementalStateRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:81: error: Module has no attribute "SyncIncrementalStateResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:85: error: Module has no attribute "EnhancedStartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:86: error: Module has no attribute "EnhancedStartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:90: error: Module has no attribute "CompileTargetRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:91: error: Module has no attribute "CompileTargetResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:95: error: Module has no attribute "ApplyFlatInstructionsRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:96: error: Module has no attribute "ApplyFlatInstructionsResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:104: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:111: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:118: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:125: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:132: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:139: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:146: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:153: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:160: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:167: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:174: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:181: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:189: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:193: error: Module has no attribute "StartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:194: error: Module has no attribute "StartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:198: error: Module has no attribute "StopFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:199: error: Module has no attribute "StopFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:203: error: Module has no attribute "UpdateStrategyRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:204: error: Module has no attribute "UpdateStrategyResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:208: error: Module has no attribute "SpawnShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:209: error: Module has no attribute "SpawnShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:213: error: Module has no attribute "PromoteShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:214: error: Module has no attribute "PromoteShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:218: error: Module has no attribute "ValidateCodeRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:219: error: Module has no attribute "ValidateCodeResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:223: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:224: error: Module has no attribute "HealthCheckResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:228: error: Module has no attribute "GetChampionStateRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:229: error: Module has no attribute "GetChampionStateResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:233: error: Module has no attribute "SyncIncrementalStateRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:234: error: Module has no attribute "SyncIncrementalStateResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:238: error: Module has no attribute "EnhancedStartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:239: error: Module has no attribute "EnhancedStartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:243: error: Module has no attribute "CompileTargetRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:244: error: Module has no attribute "CompileTargetResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:248: error: Module has no attribute "ApplyFlatInstructionsRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:249: error: Module has no attribute "ApplyFlatInstructionsResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:264: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:278: error: Module has no attribute "StartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:279: error: Module has no attribute "StartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:291: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:305: error: Module has no attribute "StopFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:306: error: Module has no attribute "StopFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:318: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:332: error: Module has no attribute "UpdateStrategyRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:333: error: Module has no attribute "UpdateStrategyResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:345: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:359: error: Module has no attribute "SpawnShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:360: error: Module has no attribute "SpawnShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:372: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:386: error: Module has no attribute "PromoteShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:387: error: Module has no attribute "PromoteShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:399: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:413: error: Module has no attribute "ValidateCodeRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:414: error: Module has no attribute "ValidateCodeResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:426: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:440: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:441: error: Module has no attribute "HealthCheckResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:453: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:467: error: Module has no attribute "GetChampionStateRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:468: error: Module has no attribute "GetChampionStateResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:480: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:494: error: Module has no attribute "SyncIncrementalStateRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:495: error: Module has no attribute "SyncIncrementalStateResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:507: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:521: error: Module has no attribute "EnhancedStartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:522: error: Module has no attribute "EnhancedStartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:534: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:548: error: Module has no attribute "CompileTargetRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:549: error: Module has no attribute "CompileTargetResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:561: error: Function is missing a type annotation  [no-untyped-def]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:575: error: Module has no attribute "ApplyFlatInstructionsRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/fuzzing_control_pb2_grpc.py:576: error: Module has no attribute "ApplyFlatInstructionsResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "HealthCheckResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "InstanceStatus"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "PerformanceMetrics"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "PromoteShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "PromoteShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "SpawnShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "SpawnShadowResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StartFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StopFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StopFuzzerResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "SystemMetrics"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "UpdateStrategyRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "UpdateStrategyResponse"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "ValidateCodeRequest"  [attr-defined]
fuzzlm_agent/infrastructure/grpc/__init__.py:12: error: Module "fuzzlm_agent.infrastructure.grpc.fuzzing_control_pb2" has no attribute "ValidateCodeResponse"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:61: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:124: error: Module has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:138: error: Module has no attribute "StartFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:141: error: Module has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:143: error: Module has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:186: error: Module has no attribute "StopFuzzerRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:222: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:269: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:319: error: Module has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:332: error: Module has no attribute "UpdateStrategyRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:375: error: Module has no attribute "StrategyConfig"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:388: error: Module has no attribute "SpawnShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:428: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:433: error: Module has no attribute "FuzzerType"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:441: error: Module has no attribute "PromoteShadowRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:482: error: Module has no attribute "ValidateCodeRequest"  [attr-defined]
fuzzlm_agent/infrastructure/runtime_client.py:531: error: Module has no attribute "HealthCheckRequest"  [attr-defined]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:19: error: Module "fuzzlm_agent.infrastructure.shared_memory.telemetry_pb2" has no attribute "TelemetryDataType"  [attr-defined]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:19: error: Module "fuzzlm_agent.infrastructure.shared_memory.telemetry_pb2" has no attribute "TelemetryEntry"  [attr-defined]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:433: error: Returning Any from function declared to return "str"  [no-any-return]
fuzzlm_agent/infrastructure/shared_memory/telemetry_stream.py:438: error: Returning Any from function declared to return "int"  [no-any-return]
Found 145 errors in 5 files (checked 28 source files)
