/*!
遥测数据生产者 - 基于protobuf格式
负责将 Fuzzer 实例的原始性能数据写入共享内存，使用protobuf以实现跨语言兼容性
*/

use anyhow::{anyhow, Result};
use log::{debug, error, info};
use prost::Message;
use std::path::{Path, PathBuf};
use std::sync::Arc;
use std::time::{SystemTime, UNIX_EPOCH};
use tokio::sync::Mutex;

// 导入protobuf定义
use crate::generated::telemetry::{
    telemetry_entry, ExecutionStats, CoverageHit, CrashFound, CorpusGrow, MutatorStats,
    TelemetryDataType, TelemetryEntry,
};

/// 共享内存环形缓冲区（Rust 端 - 生产者） - 基于protobuf
pub struct SharedMemoryRing {
    memory: Arc<Mutex<memmap2::MmapMut>>,
    metadata_size: usize,
    data_size: usize,
    write_pos: Arc<Mutex<usize>>,
    sequence_number: Arc<Mutex<u64>>,
}

impl SharedMemoryRing {
    /// 创建共享内存环形缓冲区（生产者模式）
    pub async fn create_producer(name: &str, size: usize) -> Result<Self> {
        use memmap2::MmapOptions;
        use std::fs::OpenOptions;
        use std::os::unix::fs::OpenOptionsExt;

        // 支持通过环境变量配置共享内存路径，默认使用runtime/temp
        let base_path = std::env::var("FUZZLM_SHM_DIR").unwrap_or_else(|_| {
            // 尝试使用项目根目录下的runtime/temp
            let current_dir = std::env::current_dir().unwrap_or_else(|_| PathBuf::from("."));
            let mut runtime_path = current_dir;
            // 如果在fuzzing-engine目录中，回到项目根目录
            if runtime_path.ends_with("fuzzing-engine") {
                runtime_path.pop();
                runtime_path.pop();
            }
            runtime_path
                .join("runtime")
                .join("temp")
                .to_string_lossy()
                .to_string()
        });

        let shm_path = format!("{base_path}/fuzzlm_telemetry_{name}");

        // 确保目录存在
        if let Some(parent) = Path::new(&shm_path).parent() {
            std::fs::create_dir_all(parent)?;
        }

        // 创建共享内存文件
        let file = OpenOptions::new()
            .read(true)
            .write(true)
            .create(true)
            .truncate(true)
            .mode(0o666)
            .open(&shm_path)?;

        // 设置文件大小
        file.set_len(size as u64)?;

        // 内存映射
        let mmap = unsafe { MmapOptions::new().map_mut(&file)? };

        let metadata_size = 64; // 元数据区域大小
        let data_size = size - metadata_size;

        let ring = Self {
            memory: Arc::new(Mutex::new(mmap)),
            metadata_size,
            data_size,
            write_pos: Arc::new(Mutex::new(0)),
            sequence_number: Arc::new(Mutex::new(0)),
        };

        // 初始化元数据
        ring.write_metadata(0, 0).await?;

        info!(
            "✓ 共享内存环形缓冲区创建成功: {} ({}KB) - protobuf格式",
            shm_path,
            size / 1024
        );

        Ok(ring)
    }

    /// 写入元数据
    async fn write_metadata(&self, write_pos: usize, seq_num: u64) -> Result<()> {
        let mut memory = self.memory.lock().await;

        let timestamp_ns = SystemTime::now().duration_since(UNIX_EPOCH)?.as_nanos() as u64;

        // 格式: magic(4) + version(4) + write_pos(8) + seq_num(8) + timestamp(8) + reserved(32)
        let magic = 0x54454C45u32; // "TELE"
        let version = 2u32; // 版本2表示protobuf格式

        // 写入元数据
        memory[0..4].copy_from_slice(&magic.to_le_bytes());
        memory[4..8].copy_from_slice(&version.to_le_bytes());
        memory[8..16].copy_from_slice(&(write_pos as u64).to_le_bytes());
        memory[16..24].copy_from_slice(&seq_num.to_le_bytes());
        memory[24..32].copy_from_slice(&timestamp_ns.to_le_bytes());
        
        // 保留区域 32..64 清零
        for i in 32..64 {
            memory[i] = 0;
        }

        Ok(())
    }

    /// 写入protobuf遥测条目
    pub async fn write_entry(&self, entry: &TelemetryEntry) -> Result<()> {
        // 序列化protobuf消息
        let mut entry_bytes = Vec::new();
        entry.encode(&mut entry_bytes)?;
        
        let entry_size = entry_bytes.len();

        if entry_size > self.data_size - 4 { // 保留4字节给长度
            return Err(anyhow!("条目太大: {} > {}", entry_size, self.data_size - 4));
        }

        let mut memory = self.memory.lock().await;
        let mut write_pos = self.write_pos.lock().await;
        let mut seq_num = self.sequence_number.lock().await;

        let current_pos = *write_pos;
        let total_size = 4 + entry_size; // 长度(4字节) + protobuf数据
        let new_pos = (current_pos + total_size) % self.data_size;

        // 写入数据到共享内存
        let data_offset = self.metadata_size;

        if current_pos + total_size <= self.data_size {
            // 数据不会回绕
            let start = data_offset + current_pos;
            
            // 写入长度字段
            memory[start..start + 4].copy_from_slice(&(entry_size as u32).to_le_bytes());
            
            // 写入protobuf数据
            memory[start + 4..start + 4 + entry_size].copy_from_slice(&entry_bytes);
        } else {
            // 数据需要回绕（复杂情况，先简化实现）
            return Err(anyhow!("环形缓冲区回绕暂未实现"));
        }

        // 更新位置和序列号
        *write_pos = new_pos;
        *seq_num += 1;

        // 释放锁后更新元数据
        drop(memory);
        drop(write_pos);
        drop(seq_num);

        self.write_metadata(new_pos, *self.sequence_number.lock().await)
            .await?;

        debug!(
            "遥测条目已写入: {:?}, 大小: {} (protobuf)",
            entry.data_type(), 
            total_size
        );

        Ok(())
    }

    /// 获取统计信息
    pub async fn get_stats(&self) -> (usize, u64) {
        let write_pos = *self.write_pos.lock().await;
        let seq_num = *self.sequence_number.lock().await;
        (write_pos, seq_num)
    }
}

/// 遥测数据生产者 - 基于protobuf
pub struct TelemetryProducer {
    ring_buffer: Arc<SharedMemoryRing>,
    stats: Arc<Mutex<ProducerStats>>,
}

#[derive(Debug, Default)]
pub struct ProducerStats {
    entries_written: u64,
    bytes_written: u64,
    errors: u64,
    last_write_time: Option<SystemTime>,
}

impl TelemetryProducer {
    /// 创建遥测生产者
    pub async fn new(stream_name: &str, buffer_size: usize) -> Result<Self> {
        let ring_buffer = SharedMemoryRing::create_producer(stream_name, buffer_size).await?;

        Ok(Self {
            ring_buffer: Arc::new(ring_buffer),
            stats: Arc::new(Mutex::new(ProducerStats::default())),
        })
    }

    /// 发送执行统计数据
    pub async fn send_execution_stats(
        &self,
        instance_id: String,
        executions: u32,
        exec_per_sec: f32,
        corpus_size: u32,
        crashes: u32,
    ) {
        // Phase 3.1: 添加遥测数据验证逻辑
        let validated_crashes = if crashes > executions {
            debug!(
                "遥测数据验证警告: 崩溃数超过执行数 (crashes={crashes}, executions={executions})"
            );
            executions
        } else {
            crashes
        };

        let capped_exec_per_sec = if exec_per_sec > 1_000_000.0 {
            debug!(
                "遥测数据验证警告: 执行速度异常高 (exec/s={exec_per_sec}), 限制到100万/秒"
            );
            1_000_000.0
        } else {
            exec_per_sec
        };

        // 创建protobuf消息
        let execution_stats = ExecutionStats {
            executions,
            exec_per_sec: capped_exec_per_sec,
            corpus_size,
            crashes: validated_crashes,
        };

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::ExecutionCount.into(),
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            payload: Some(telemetry_entry::Payload::ExecutionStats(execution_stats)),
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送执行统计失败: {e}");
        }
    }

    /// 发送覆盖率命中数据
    pub async fn send_coverage_hit(
        &self,
        instance_id: String,
        edge_id: u32,
        hit_count: u32,
        is_new: bool,
    ) {
        let coverage_hit = CoverageHit {
            edge_id,
            hit_count,
            is_new,
        };

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CoverageHit.into(),
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            payload: Some(telemetry_entry::Payload::CoverageHit(coverage_hit)),
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送覆盖率命中失败: {e}");
        }
    }

    /// 发送崩溃发现数据
    pub async fn send_crash_found(
        &self,
        instance_id: String,
        crash_type: String,
        input_hash: u64,
        signal: u32,
    ) {
        let crash_found = CrashFound {
            crash_type,
            input_hash,
            signal,
        };

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CrashFound.into(),
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            payload: Some(telemetry_entry::Payload::CrashFound(crash_found)),
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送崩溃发现失败: {e}");
        }
    }

    /// 发送语料库增长数据
    pub async fn send_corpus_grow(
        &self,
        instance_id: String,
        new_inputs: u32,
        total_size: u32,
        avg_length: u32,
    ) {
        let corpus_grow = CorpusGrow {
            new_inputs,
            total_size,
            avg_length,
        };

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::CorpusGrow.into(),
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            payload: Some(telemetry_entry::Payload::CorpusGrow(corpus_grow)),
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送语料库增长失败: {e}");
        }
    }

    /// 发送变异器统计数据
    pub async fn send_mutator_stats(
        &self,
        instance_id: String,
        mutator_id: u32,
        usage_count: u32,
        success_rate: f32,
    ) {
        let mutator_stats = MutatorStats {
            mutator_id,
            usage_count,
            success_rate,
        };

        let entry = TelemetryEntry {
            data_type: TelemetryDataType::MutatorStats.into(),
            instance_id,
            timestamp_ns: SystemTime::now()
                .duration_since(UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as u64,
            payload: Some(telemetry_entry::Payload::MutatorStats(mutator_stats)),
        };

        if let Err(e) = self.write_entry(entry).await {
            error!("发送变异器统计失败: {e}");
        }
    }

    /// 写入遥测条目
    async fn write_entry(&self, entry: TelemetryEntry) -> Result<()> {
        let entry_size = entry.encoded_len();
        let result = self.ring_buffer.write_entry(&entry).await;

        // 更新统计信息
        {
            let mut stats = self.stats.lock().await;

            match &result {
                Ok(_) => {
                    stats.entries_written += 1;
                    stats.bytes_written += entry_size as u64;
                    stats.last_write_time = Some(SystemTime::now());
                }
                Err(_) => {
                    stats.errors += 1;
                }
            }
        }

        result
    }

    /// 获取生产者统计信息
    pub async fn get_stats(&self) -> ProducerStats {
        self.stats.lock().await.clone()
    }

    /// 获取缓冲区统计信息
    pub async fn get_buffer_stats(&self) -> (usize, u64) {
        self.ring_buffer.get_stats().await
    }
}

impl Clone for ProducerStats {
    fn clone(&self) -> Self {
        Self {
            entries_written: self.entries_written,
            bytes_written: self.bytes_written,
            errors: self.errors,
            last_write_time: self.last_write_time,
        }
    }
}