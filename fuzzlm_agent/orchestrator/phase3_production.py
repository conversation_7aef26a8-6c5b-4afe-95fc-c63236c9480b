"""Phase 3: Production Run and Adaptive State Perception
====================================================

This module handles production run and adaptive state perception as described in
docs/workflow.md Phase 3. It starts the champion fuzzer, monitors telemetry data,
and periodically sends data to LLM for state awareness analysis.

This is a standalone, simplified implementation focused on research prototype
principles. It is the ONLY phase that needs significant async code for monitoring.
"""

from __future__ import annotations

import asyncio
import logging
from dataclasses import dataclass, field
from datetime import datetime, timedelta, timezone
from typing import TYPE_CHECKING, Any

from fuzzlm_agent.core import (
    PerformanceMetrics,
    TelemetryAnalyzer,
)
from fuzzlm_agent.prompts import PromptManager

if TYPE_CHECKING:
    from fuzzlm_agent.infrastructure.litellm_client import LiteLLMClient
    from fuzzlm_agent.infrastructure.runtime_client import RuntimeClient
    from fuzzlm_agent.infrastructure.shared_memory import TelemetryReader
    from fuzzlm_agent.orchestrator.campaign_orchestrator import CampaignContext

logger = logging.getLogger(__name__)

# Initialize prompt manager (global instance for performance)
_prompt_manager = PromptManager()


@dataclass
class TelemetrySnapshot:
    """Snapshot of telemetry data for a time window."""

    window_start: datetime
    window_end: datetime
    total_executions: int = 0
    coverage_hits: int = 0
    unique_crashes: int = 0
    queue_size: int = 0
    corpus_size: int = 0
    energy_level: float = 0.0
    memory_usage_mb: float = 0.0

    def summarize(self) -> str:
        """Create a summary for LLM analysis."""
        duration = (self.window_end - self.window_start).total_seconds()
        exec_rate = self.total_executions / duration if duration > 0 else 0

        return f"""Telemetry data (last {duration:.0f} seconds):
- Total executions: {self.total_executions:,}
- Execution rate: {exec_rate:.1f} exec/sec
- Coverage hits: {self.coverage_hits:,}
- Unique crashes: {self.unique_crashes}
- Queue size: {self.queue_size}
- Corpus size: {self.corpus_size}
- Energy level: {self.energy_level:.2f}
- Memory usage: {self.memory_usage_mb:.1f} MB"""


@dataclass
class StateAnalysis:
    """LLM's analysis of fuzzing state."""

    timestamp: datetime
    current_stage: str  # exploration, exploitation, stagnation
    confidence: float
    bottlenecks: list[str] = field(default_factory=list)
    recommendations: list[str] = field(default_factory=list)
    needs_adjustment: bool = False
    suggested_actions: list[str] = field(default_factory=list)
    raw_response: str = ""


async def phase3_production_run(
    ctx: CampaignContext,  # CampaignContext
    runtime_client: RuntimeClient,  # RuntimeClient
    llm_client: LiteLLMClient,
    telemetry_reader: TelemetryReader | None,  # TelemetryReader
    duration_hours: float,
) -> Any:  # Returns updated CampaignContext
    """Run production fuzzing with adaptive state perception.

    This function:
    1. Starts champion fuzzer with initial strategy
    2. Monitors telemetry data stream asynchronously using high-performance TelemetryAnalyzer
    3. Periodically sends telemetry to LLM for state awareness (every 10 min)
    4. Detects performance anomalies in real-time
    5. Updates ctx with comprehensive performance metrics and reports
    6. Sets ctx.needs_adjustment flag based on LLM analysis and anomalies

    Args:
        ctx: Campaign context with initial strategy
        runtime_client: Client for controlling fuzzing runtime
        llm_client: LLM client for state analysis
        telemetry_reader: Reader for telemetry data stream
        duration_hours: How long to run production fuzzing

    Returns:
        Updated campaign context with metrics and adjustment flag

    """
    logger.info(f"Phase 3: Starting production run for {duration_hours} hours")

    # Ensure metadata field exists
    if not hasattr(ctx, "metadata"):
        ctx.metadata = {}

    # Validate strategy exists
    if ctx.strategy is None:
        msg = "Strategy not available for production run"
        raise ValueError(msg)

    # Start champion fuzzer
    logger.info("Starting champion fuzzer with initial strategy")
    try:
        fuzzer_id = await runtime_client.start_fuzzer(
            fuzzer_type="champion",
            strategy=ctx.strategy,
            target_path=ctx.target_path,
        )
        ctx.metadata["champion_fuzzer_id"] = fuzzer_id
        logger.info(f"Champion fuzzer started with ID: {fuzzer_id}")
    except Exception as e:
        logger.error(f"Failed to start champion fuzzer: {e}")
        ctx.metadata["phase3_error"] = str(e)
        return ctx

    # Initialize telemetry analyzer
    telemetry_analyzer = TelemetryAnalyzer()

    # Set up monitoring parameters
    datetime.now(timezone.utc)
    duration = timedelta(hours=duration_hours)

    # State tracking
    state_analyses = []
    needs_adjustment = False
    performance_anomalies = []

    # Callback for real-time anomaly detection
    async def anomaly_callback(
        metrics: PerformanceMetrics,
        anomalies: list[str],
    ) -> None:
        logger.warning(f"Performance anomalies detected: {anomalies}")
        performance_anomalies.extend(anomalies)

        # Trigger immediate LLM analysis if critical anomalies
        if any("dropped significantly" in a or "stagnation" in a for a in anomalies):
            analysis = await _analyze_state_with_metrics(
                llm_client,
                metrics,
                ctx,
                anomalies=anomalies,
            )
            state_analyses.append(analysis)

            if analysis.needs_adjustment:
                nonlocal needs_adjustment
                needs_adjustment = True

    try:
        # Validate telemetry reader is provided
        if telemetry_reader is None:
            msg = "Telemetry reader is required for production monitoring"
            raise ValueError(msg)

        # Run telemetry analysis with periodic LLM state perception
        metrics_history = await telemetry_analyzer.analyze_telemetry_stream(
            telemetry_reader,
            duration,
            callback=anomaly_callback,
        )

        # Perform LLM analysis on collected windows
        for i, metrics in enumerate(metrics_history):
            if i % 10 == 0:  # Every 10 windows (10 minutes)
                logger.info(f"Sending metrics window {i} to LLM for state analysis")
                analysis = await _analyze_state_with_metrics(
                    llm_client,
                    metrics,
                    ctx,
                )
                state_analyses.append(analysis)

                if analysis.needs_adjustment:
                    logger.warning(
                        "LLM detected need for adjustment: %s",
                        analysis.bottlenecks,
                    )
                    needs_adjustment = True

    except Exception as e:
        logger.exception("Error during production monitoring: %s", e)
        ctx.metadata["phase3_monitoring_error"] = str(e)

    finally:
        # Stop champion fuzzer
        try:
            logger.info("Stopping champion fuzzer")
            await runtime_client.stop_fuzzer(fuzzer_id)
        except Exception as e:
            logger.exception("Failed to stop fuzzer: %s", e)

    # Generate performance report
    performance_report = telemetry_analyzer.generate_performance_report()

    # Update context with results
    ctx.metadata.update(
        {
            "phase3_metrics_history": [m.__dict__ for m in metrics_history],
            "phase3_state_analyses": state_analyses,
            "phase3_needs_adjustment": needs_adjustment,
            "phase3_performance_report": performance_report,
            "phase3_anomalies": performance_anomalies,
            "phase3_duration_hours": duration_hours,
        },
    )

    logger.info(
        f"Phase 3 completed. Collected {len(metrics_history)} metric windows. "
        f"Needs adjustment: {needs_adjustment}",
    )
    return ctx


async def _read_telemetry_batch(
    telemetry_reader: TelemetryReader | None,
) -> list[dict[str, Any]]:
    """Read a batch of telemetry entries."""
    if telemetry_reader is None:
        # 必须有真实的telemetry reader
        msg = "Telemetry reader is required for production monitoring"
        raise ValueError(msg)

    try:
        # Read available telemetry (non-blocking)
        entries = []

        # Try to read up to 100 entries
        for _ in range(100):
            entry = await telemetry_reader.read_entry(timeout=0.01)
            if entry is None:
                break
            entries.append(entry)

    except Exception as e:
        logger.debug("Error reading telemetry: %s", e)
        return []
    else:
        return entries


def _update_snapshot(snapshot: TelemetrySnapshot, entry: dict[str, Any]) -> None:
    """Update telemetry snapshot with new entry."""
    entry_type = entry.get("type", "")

    if entry_type == "execution_count":
        snapshot.total_executions += entry.get("count", 0)
    elif entry_type == "coverage_hit":
        snapshot.coverage_hits += 1
    elif entry_type == "crash_found":
        if entry.get("is_unique", False):
            snapshot.unique_crashes += 1
    elif entry_type == "queue_update":
        snapshot.queue_size = entry.get("size", snapshot.queue_size)
    elif entry_type == "corpus_grow":
        snapshot.corpus_size = entry.get("size", snapshot.corpus_size)
    elif entry_type == "energy_update":
        snapshot.energy_level = entry.get("level", snapshot.energy_level)
    elif entry_type == "memory_usage":
        snapshot.memory_usage_mb = entry.get("usage_mb", snapshot.memory_usage_mb)


async def _analyze_state_with_llm(
    llm_client: Any,
    snapshot: TelemetrySnapshot,
    ctx: Any,
) -> StateAnalysis:
    """Send telemetry to LLM for state analysis."""
    try:
        # Use PromptManager to generate the state analysis prompt
        prompt = _prompt_manager.get_prompt(
            "analysis.state_analysis",
            target_path=ctx.target_path,
            strategy_name=ctx.strategy.name,
            elapsed_hours=ctx.elapsed_hours,
            telemetry_summary=snapshot.summarize(),
        )

    except Exception as e:
        logger.error(f"Failed to generate state analysis prompt: {e}")
        # Fallback to basic prompt if template fails
        prompt = f"""Analyze fuzzing campaign state for {ctx.target_path}.

Current strategy: {ctx.strategy.name}
Duration: {ctx.elapsed_hours:.1f} hours

{snapshot.summarize()}

Respond in JSON format with current_stage, confidence, bottlenecks, needs_adjustment, and suggested_actions."""

    try:
        # 使用LiteLLMClient同步API
        response_text = llm_client.generate(
            prompt=prompt,
            max_tokens=500,
            temperature=0.3,
        )

        # Parse response using json_repair
        import json_repair

        analysis_data = json_repair.loads(response_text)

        # 确保 analysis_data 是有效的字典且不为 None
        if analysis_data is None or not isinstance(analysis_data, dict):
            msg = "JSON parsing failed: response is not a valid JSON object"
            raise ValueError(msg)

        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage=analysis_data.get("current_stage", "unknown"),
            confidence=float(analysis_data.get("confidence", 0.5)),
            bottlenecks=analysis_data.get("bottlenecks", []),
            needs_adjustment=bool(analysis_data.get("needs_adjustment", False)),
            suggested_actions=analysis_data.get("suggested_actions", []),
            raw_response=response_text,
        )

    except Exception as e:
        logger.exception("Failed to analyze state with LLM: %s", e)
        # Return default analysis
        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage="unknown",
            confidence=0.0,
            bottlenecks=[f"Analysis failed: {e!s}"],
            needs_adjustment=False,
        )


def _update_context_with_results(
    ctx: Any,
    snapshots: list[TelemetrySnapshot],
    analyses: list[StateAnalysis],
    needs_adjustment: bool,
) -> None:
    """Update campaign context with phase 3 results."""
    # Calculate aggregate metrics
    if snapshots:
        total_execs = sum(s.total_executions for s in snapshots)
        total_crashes = sum(s.unique_crashes for s in snapshots)
        final_coverage = snapshots[-1].coverage_hits if snapshots else 0

        # Update context metrics
        ctx.total_executions = total_execs
        ctx.unique_crashes = total_crashes
        ctx.final_coverage = final_coverage / 65536.0  # Normalize to percentage

    # Store analyses
    ctx.metadata["state_analyses"] = [
        {
            "stage": a.current_stage,
            "confidence": a.confidence,
            "bottlenecks": a.bottlenecks,
            "needs_adjustment": a.needs_adjustment,
        }
        for a in analyses
    ]

    # Ensure metadata field exists
    if not hasattr(ctx, "metadata"):
        ctx.metadata = {}

    # Set adjustment flag
    ctx.metadata["needs_adjustment"] = needs_adjustment

    # Identify primary bottleneck
    if analyses and analyses[-1].bottlenecks:
        ctx.metadata["primary_bottleneck"] = analyses[-1].bottlenecks[0]

    # Update phase completion
    ctx.metadata["phase3_completed"] = datetime.now(timezone.utc).isoformat()
    ctx.metadata["phase3_duration_hours"] = ctx.elapsed_hours


async def _analyze_state_with_metrics(
    llm_client: LiteLLMClient,
    metrics: PerformanceMetrics,
    ctx: CampaignContext,
    anomalies: list[str] | None = None,
) -> StateAnalysis:
    """Analyze fuzzing state using performance metrics."""
    # Format metrics for LLM
    metrics_summary = {
        "window_duration_seconds": (
            metrics.window_end - metrics.window_start
        ).total_seconds(),
        "total_executions": metrics.total_executions,
        "execution_rate": metrics.execution_rate,
        "unique_edges": metrics.unique_edges,
        "path_discovery_rate": metrics.path_discovery_rate,
        "crashes_found": metrics.crashes_found,
        "crash_rate": metrics.crash_rate,
        "corpus_size": metrics.corpus_size,
        "corpus_growth_rate": metrics.corpus_growth_rate,
        "efficiency_score": metrics.efficiency_score,
        "anomalies": anomalies or [],
    }

    # Use prompt manager to generate state analysis prompt
    try:
        prompt = _prompt_manager.get_prompt(
            "analysis.state_analysis",
            target_path=ctx.target_path,
            strategy_name=(
                ctx.strategy.get("name", "unknown") if ctx.strategy else "unknown"
            ),
            elapsed_hours=getattr(ctx, "elapsed_hours", 0),
            telemetry_summary=metrics_summary,
        )
    except Exception as e:
        logger.error(f"Failed to generate state analysis prompt: {e}")
        # Fallback prompt
        prompt = f"""Analyze fuzzing campaign state:
Target: {ctx.target_path}
Metrics: {metrics_summary}

Provide analysis in JSON format with: state, confidence, bottlenecks, recommendations, needs_adjustment"""

    # Get LLM analysis
    response = await asyncio.to_thread(llm_client.generate, prompt)

    # Parse response
    try:
        analysis_data = _parse_analysis_response(response)
        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage=analysis_data.get("state", "unknown"),
            confidence=analysis_data.get("confidence", 0.5),
            bottlenecks=analysis_data.get("bottlenecks", []),
            recommendations=analysis_data.get("recommendations", []),
            needs_adjustment=analysis_data.get("needs_adjustment", False),
        )
    except Exception as e:
        logger.error(f"Failed to parse state analysis: {e}")
        return StateAnalysis(
            timestamp=datetime.now(timezone.utc),
            current_stage="error",
            confidence=0.0,
            bottlenecks=["Failed to analyze state"],
            recommendations=[],
            needs_adjustment=False,
        )


def _parse_analysis_response(response: str) -> dict[str, Any]:
    """Parse LLM response for state analysis.

    Args:
        response: Raw LLM response

    Returns:
        Parsed analysis data with state, confidence, bottlenecks, etc.

    """
    import json_repair

    try:
        # Try to extract JSON from response
        parsed = json_repair.loads(response)

        # Ensure we have a dictionary
        if not isinstance(parsed, dict):
            logger.warning(f"Expected dict from LLM response, got {type(parsed)}")
            # Try to extract dict from response if it's wrapped
            if isinstance(parsed, str):
                # Try parsing again if it's a string
                parsed = json_repair.loads(parsed)

            if not isinstance(parsed, dict):
                msg = f"Expected dict, got {type(parsed)}"
                raise ValueError(msg)

        data = parsed

        # Validate required fields
        required_fields = [
            "state",
            "confidence",
            "bottlenecks",
            "recommendations",
            "needs_adjustment",
        ]
        for field in required_fields:
            if field not in data:
                logger.warning(f"Missing required field in analysis response: {field}")
                # Provide default values
                if field == "state":
                    data[field] = "unknown"
                elif field == "confidence":
                    data[field] = 0.5
                elif field in ["bottlenecks", "recommendations"]:
                    data[field] = []
                elif field == "needs_adjustment":
                    data[field] = False

        # Ensure correct types
        data["confidence"] = float(data.get("confidence", 0.5))
        data["needs_adjustment"] = bool(data.get("needs_adjustment", False))

        # Ensure lists for bottlenecks and recommendations
        if not isinstance(data.get("bottlenecks"), list):
            data["bottlenecks"] = []
        if not isinstance(data.get("recommendations"), list):
            data["recommendations"] = []

        return data

    except Exception as e:
        logger.error(
            f"Failed to parse analysis response: {e}. Response: {response[:200]}...",
        )
        # Return minimal valid structure
        return {
            "state": "parse_error",
            "confidence": 0.0,
            "bottlenecks": [f"Failed to parse LLM response: {e!s}"],
            "recommendations": [],
            "needs_adjustment": False,
        }
