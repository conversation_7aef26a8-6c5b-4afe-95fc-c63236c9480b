🔧 生成gRPC Protocol Buffers代码...
📝 生成Python gRPC代码...
[0;32m✓ Python gRPC代码生成成功[0m
  生成文件: fuzzing_control_pb2.py, fuzzing_control_pb2_grpc.py
🔧 修复导入路径...
[0;32m   ✅ 已修复 fuzzing_control_pb2_grpc.py 的导入路径[0m
📝 生成LLM代码生成协议...
[0;32m✓ LLM协议代码生成成功[0m
  生成文件: llm_generation_pb2.py, llm_generation_pb2_grpc.py
📝 生成遥测数据protobuf...
[0;32m✓ 遥测protobuf代码生成成功[0m
  生成文件: telemetry_pb2.py

[0;32m🎉 Protocol Buffers代码生成完成！[0m

下一步：
1. Python端可以直接导入使用生成的gRPC和遥测protobuf代码
2. Rust端通过build.rs自动编译protobuf文件
3. 遥测数据现在使用protobuf格式，提供更好的跨语言兼容性
